import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { Home, Target, BarChart3, Calendar, Database, Upload, User, LogOut } from 'lucide-react';
import TrainingPage from './pages/TrainingPage';
import ReviewPage from './pages/ReviewPage';
import StatsPage from './pages/StatsPage';
import CalendarPage from './pages/CalendarPage';
import SentenceDetailPage from './pages/SentenceDetailPage';
import OnboardingPage from './pages/OnboardingPage';
import SimpleDataImporter from './components/SimpleDataImporter';
import DatabaseInitializer from './components/DatabaseInitializer';
import FirestoreTestPanel from './components/FirestoreTestPanel';
import AuthModal from './components/AuthModal';
import WelcomePage from './components/WelcomePage.jsx';
import { databaseAPI } from './utils/firestoreDatabaseAPI.js';
import { authManager } from './utils/auth.js';
import { localStorageManager } from './utils/localStorageManager.js';

const AppContainer = styled.div`
  min-height: 100vh;
  background: #f7fafc;
`;

const Navigation = styled.nav`
  background: white;
  border-bottom: 1px solid #e2e8f0;
  padding: 1rem 2rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
`;

const NavContainer = styled.div`
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 1rem;

  @media (min-width: 1200px) {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
  }
`;

const Logo = styled.h1`
  color: #2d3748;
  font-size: 1.2rem;
  font-weight: 700;
  margin: 0;
  white-space: nowrap;

  @media (min-width: 768px) {
    font-size: 1.5rem;
  }
`;

const NavMenu = styled.div`
  display: flex;
  gap: 0.5rem;
  align-items: center;
  flex-wrap: wrap;

  @media (min-width: 768px) {
    gap: 1rem;
    flex-wrap: nowrap;
  }
`;

const UserSection = styled.div`
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-left: 1rem;
  padding-left: 1rem;
  border-left: 1px solid #e2e8f0;
`;

const UserInfo = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #4a5568;
  font-size: 0.9rem;
`;

const UserAvatar = styled.div`
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea, #764ba2);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 0.8rem;
`;

const LogoutButton = styled.button`
  background: none;
  border: 1px solid #e2e8f0;
  color: #4a5568;
  padding: 0.5rem;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.25rem;

  &:hover {
    border-color: #f56565;
    color: #f56565;
  }
`;

const NavButton = styled.button.withConfig({
  shouldForwardProp: (prop) => !['active'].includes(prop),
})`
  background: ${props => props.active ? 'linear-gradient(135deg, #667eea, #764ba2)' : 'transparent'};
  color: ${props => props.active ? 'white' : '#4a5568'};
  border: 2px solid ${props => props.active ? 'transparent' : '#e2e8f0'};
  border-radius: 8px;
  padding: 0.4rem 0.8rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.3rem;
  font-size: 0.9rem;
  white-space: nowrap;

  @media (min-width: 768px) {
    padding: 0.5rem 1rem;
    gap: 0.5rem;
    font-size: 1rem;
  }

  &:hover {
    background: ${props => props.active ? 'linear-gradient(135deg, #667eea, #764ba2)' : '#f7fafc'};
    border-color: ${props => props.active ? 'transparent' : '#cbd5e0'};
  }
`;

function App() {
  const [currentPage, setCurrentPage] = useState('training');
  const [selectedSentenceId, setSelectedSentenceId] = useState(null);
  const [showOnboarding, setShowOnboarding] = useState(false);
  const [showDatabaseInit, setShowDatabaseInit] = useState(false);
  const [isDatabaseReady, setIsDatabaseReady] = useState(false);
  const [currentUser, setCurrentUser] = useState(null);
  const [showAuthModal, setShowAuthModal] = useState(false);
  const [isExperienceMode, setIsExperienceMode] = useState(false);
  const [showWelcome, setShowWelcome] = useState(true);

  useEffect(() => {
    // 监听认证状态变化
    const handleAuthStateChange = (user) => {
      setCurrentUser(user);
      if (user) {
        console.log('用户已登录:', user.email);
        setIsExperienceMode(false);
        setShowWelcome(false);
        checkDatabaseAndOnboarding();
      } else {
        console.log('用户未登录');
        // 不自动显示认证模态框，而是显示欢迎页面
        if (!isExperienceMode) {
          setShowWelcome(true);
        }
      }
    };

    authManager.addAuthStateListener(handleAuthStateChange);

    return () => {
      authManager.removeAuthStateListener(handleAuthStateChange);
    };
  }, []);

  const checkDatabaseAndOnboarding = async () => {
    try {
      // 先初始化数据库API
      const initSuccess = await databaseAPI.initialize();
      if (!initSuccess) {
        console.log('数据库API初始化失败，显示数据库初始化页面');
        setShowDatabaseInit(true);
        return;
      }

      // 检查数据库状态
      const dbStats = await databaseAPI.getDatabaseStatistics();

      if (!dbStats || dbStats.sentences === 0) {
        // 数据库未初始化，显示数据库初始化页面
        console.log('数据库为空，显示数据库初始化页面');
        setShowDatabaseInit(true);
        return;
      }

      console.log('数据库已就绪，句子数量:', dbStats.sentences);
      setIsDatabaseReady(true);

      // 检查用户是否已经完成引导
      const onboardingCompleted = localStorage.getItem('onboardingCompleted');
      if (!onboardingCompleted) {
        setShowOnboarding(true);
      }
    } catch (error) {
      console.error('检查数据库状态失败:', error);
      // 如果检查失败，显示数据库初始化页面
      setShowDatabaseInit(true);
    }
  };

  const handleSentenceDetail = (sentenceId) => {
    setSelectedSentenceId(sentenceId);
    setCurrentPage('sentence-detail');
  };

  const handleBackFromDetail = () => {
    setCurrentPage('training');
    setSelectedSentenceId(null);
  };

  const handleOnboardingComplete = () => {
    setShowOnboarding(false);
  };

  const handleDatabaseInitComplete = () => {
    setShowDatabaseInit(false);
    setIsDatabaseReady(true);

    // 检查是否需要显示引导页面
    const onboardingCompleted = localStorage.getItem('onboardingCompleted');
    if (!onboardingCompleted) {
      setShowOnboarding(true);
    }
  };

  const handleAuthSuccess = (user) => {
    setCurrentUser(user);
    setShowAuthModal(false);
    setShowWelcome(false);
    setIsExperienceMode(false);
    console.log('认证成功:', user);
  };

  const handleExperienceMode = async () => {
    console.log('启动体验模式');
    setIsExperienceMode(true);
    setShowWelcome(false);
    setCurrentUser(null);

    // 初始化本地存储管理器
    localStorageManager.initializeExperienceMode();

    // 设置虚拟用户档案
    const experienceProfile = localStorageManager.loadUserProfile();
    console.log('体验模式用户档案:', experienceProfile);

    // 体验模式也需要初始化数据库以获取句子数据
    await checkDatabaseAndOnboarding();
  };

  const handleLogout = async () => {
    const result = await authManager.logout();
    if (result.success) {
      setCurrentUser(null);
      setIsExperienceMode(false);
      setShowWelcome(true);
    }
  };

  const renderCurrentPage = () => {
    switch (currentPage) {
      case 'sentence-detail':
        return (
          <SentenceDetailPage
            sentenceId={selectedSentenceId}
            onBack={handleBackFromDetail}
          />
        );
      case 'review':
        return <ReviewPage onSentenceDetail={handleSentenceDetail} />;
      case 'stats':
        return <StatsPage onSentenceDetail={handleSentenceDetail} />;
      case 'calendar':
        return <CalendarPage />;
      case 'import':
        return <SimpleDataImporter />;
      case 'test':
        return <FirestoreTestPanel />;
      case 'training':
      default:
        return <TrainingPage onSentenceDetail={handleSentenceDetail} />;
    }
  };

  // 如果需要显示数据库初始化页面，直接返回初始化页面
  if (showDatabaseInit) {
    return <DatabaseInitializer onComplete={handleDatabaseInitComplete} />;
  }

  // 如果需要显示引导页面，直接返回引导页面
  if (showOnboarding) {
    return <OnboardingPage onComplete={handleOnboardingComplete} />;
  }

  // 如果显示欢迎页面（用户未登录且未选择体验模式）
  if (showWelcome && !currentUser && !isExperienceMode) {
    return (
      <WelcomePage
        onExperienceMode={handleExperienceMode}
        onAuthSuccess={handleAuthSuccess}
      />
    );
  }

  return (
    <AppContainer>
      <Navigation>
        <NavContainer>
          <Logo>句之炼金 Sentence Alchemist</Logo>
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <NavMenu>
              <NavButton
                active={currentPage === 'training'}
                onClick={() => setCurrentPage('training')}
              >
                <Home size={18} />
                练习模式
              </NavButton>
              <NavButton
                active={currentPage === 'review'}
                onClick={() => setCurrentPage('review')}
              >
                <Target size={18} />
                智能复习
              </NavButton>
              <NavButton
                active={currentPage === 'stats'}
                onClick={() => setCurrentPage('stats')}
              >
                <BarChart3 size={18} />
                学习统计
              </NavButton>
              <NavButton
                active={currentPage === 'calendar'}
                onClick={() => setCurrentPage('calendar')}
              >
                <Calendar size={18} />
                学习日历
              </NavButton>
              <NavButton
                active={currentPage === 'import'}
                onClick={() => setCurrentPage('import')}
              >
                <Upload size={18} />
                数据导入
              </NavButton>
              {import.meta.env.DEV && (
                <NavButton
                  active={currentPage === 'test'}
                  onClick={() => setCurrentPage('test')}
                >
                  <Database size={18} />
                  测试
                </NavButton>
              )}
            </NavMenu>

            {currentUser ? (
              <UserSection>
                <UserInfo>
                  <UserAvatar>
                    {currentUser.displayName ?
                      currentUser.displayName.charAt(0).toUpperCase() :
                      currentUser.email.charAt(0).toUpperCase()
                    }
                  </UserAvatar>
                  <span>
                    {currentUser.displayName || currentUser.email}
                  </span>
                </UserInfo>
                <LogoutButton onClick={handleLogout} title="退出登录">
                  <LogOut size={16} />
                </LogoutButton>
              </UserSection>
            ) : isExperienceMode && (
              <UserSection>
                <UserInfo>
                  <UserAvatar style={{ background: '#48bb78' }}>
                    体
                  </UserAvatar>
                  <span style={{ color: '#48bb78', fontWeight: '600' }}>
                    体验模式
                  </span>
                </UserInfo>
                <LogoutButton onClick={handleLogout} title="退出体验模式">
                  <LogOut size={16} />
                </LogoutButton>
              </UserSection>
            )}
          </div>
        </NavContainer>
      </Navigation>

      {renderCurrentPage()}
    </AppContainer>
  );
}

export default App;

import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { Clock, Target, TrendingUp, AlertCircle, CheckCircle, BarChart3 } from 'lucide-react';
import QuestionArea from '../components/QuestionArea';
import OptionsArea from '../components/OptionsArea';
import FeedbackArea from '../components/FeedbackArea';
import { gameController, GAME_MODE, GAME_STATE } from '../utils/gameController';
import { reviewQueueManager, REVIEW_PRIORITY } from '../utils/reviewQueueManager';
import { sampleSentences } from '../data/sampleSentences';

const PageContainer = styled.div`
  min-height: calc(100vh - 80px);
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 1rem;
  width: 100%;
  box-sizing: border-box;
`;

const Header = styled.div`
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 1.5rem;
  margin-bottom: 1rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
`;

const HeaderTitle = styled.h1`
  color: #2d3748;
  font-size: 2rem;
  font-weight: 700;
  margin: 0 0 1rem 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
`;

const StatsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 1rem;
`;

const StatCard = styled.div`
  background: white;
  border-radius: 12px;
  padding: 1rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-left: 4px solid ${props => props.color || '#667eea'};
`;

const StatLabel = styled.div`
  font-size: 0.8rem;
  color: #718096;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 0.5rem;
`;

const StatValue = styled.div`
  font-size: 1.5rem;
  color: #2d3748;
  font-weight: 700;
  display: flex;
  align-items: center;
  gap: 0.5rem;
`;

const PrioritySection = styled.div`
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 1.5rem;
  margin-bottom: 1rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
`;

const PriorityTitle = styled.h2`
  color: #2d3748;
  font-size: 1.3rem;
  font-weight: 600;
  margin: 0 0 1rem 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
`;

const PriorityGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
`;

const PriorityCard = styled.div.withConfig({
  shouldForwardProp: (prop) => !['bgColor', 'borderColor', 'count'].includes(prop),
})`
  background: ${props => props.bgColor || '#f7fafc'};
  border: 2px solid ${props => props.borderColor || '#e2e8f0'};
  border-radius: 12px;
  padding: 1rem;
  text-align: center;
  cursor: ${props => props.count > 0 ? 'pointer' : 'default'};
  opacity: ${props => props.count > 0 ? 1 : 0.6};
  transition: all 0.2s ease;

  &:hover {
    transform: ${props => props.count > 0 ? 'translateY(-2px)' : 'none'};
    box-shadow: ${props => props.count > 0 ? '0 4px 12px rgba(0, 0, 0, 0.15)' : 'none'};
  }
`;

const PriorityLabel = styled.div`
  font-size: 0.8rem;
  color: #718096;
  font-weight: 600;
  text-transform: uppercase;
  margin-bottom: 0.5rem;
`;

const PriorityCount = styled.div`
  font-size: 1.8rem;
  font-weight: 700;
  color: ${props => props.color || '#2d3748'};
`;

const GameSection = styled.div`
  background: white;
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  margin-bottom: 1rem;
`;

const StartButton = styled.button`
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border: none;
  border-radius: 12px;
  padding: 1rem 2rem;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin: 0 auto;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
  }
`;

const ProgressSection = styled.div`
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 1.5rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
`;

const ProgressBar = styled.div`
  width: 100%;
  height: 12px;
  background: #e2e8f0;
  border-radius: 6px;
  overflow: hidden;
  margin: 1rem 0;
`;

const ProgressFill = styled.div`
  height: 100%;
  background: linear-gradient(90deg, #667eea, #764ba2);
  border-radius: 6px;
  transition: width 0.3s ease;
  width: ${props => props.progress}%;
`;

const ReviewPage = ({ onSentenceDetail }) => {
  const [reviewStats, setReviewStats] = useState(null);
  const [dailyProgress, setDailyProgress] = useState(null);
  const [gameState, setGameState] = useState(GAME_STATE.IDLE);
  const [currentQuestion, setCurrentQuestion] = useState(null);
  const [currentOptions, setCurrentOptions] = useState(null);
  const [feedback, setFeedback] = useState(null);
  const [selectedAnswer, setSelectedAnswer] = useState(null);

  useEffect(() => {
    updateReviewData();
  }, []);

  const updateReviewData = () => {
    // 更新复习队列
    reviewQueueManager.updateReviewQueues(sampleSentences);
    
    // 获取统计信息
    const stats = reviewQueueManager.getReviewStats();
    const progress = reviewQueueManager.getDailyProgress();
    
    setReviewStats(stats);
    setDailyProgress(progress);
  };

  const startReview = (priority = null) => {
    const batchSize = 10;
    const maxTime = 600; // 10分钟
    
    let reviewBatch;
    if (priority) {
      // 获取特定优先级的复习项目
      reviewBatch = reviewQueueManager.reviewQueues[priority].slice(0, batchSize);
    } else {
      // 获取混合优先级的复习批次
      reviewBatch = reviewQueueManager.getNextReviewBatch(batchSize, maxTime);
    }
    
    if (reviewBatch.length === 0) {
      alert('暂无需要复习的句子！');
      return;
    }
    
    // 启动复习模式游戏
    gameController.startGame(GAME_MODE.REVIEW, {
      reviewBatch,
      maxQuestionsPerSession: reviewBatch.length
    });
    
    setGameState(GAME_STATE.QUESTION);
    loadNextQuestion();
  };

  const loadNextQuestion = () => {
    const question = gameController.getCurrentQuestion();
    const options = gameController.getCurrentOptions();
    
    if (question && options) {
      setCurrentQuestion(question);
      setCurrentOptions(options);
      setSelectedAnswer(null);
      setFeedback(null);
    } else {
      // 复习完成
      setGameState(GAME_STATE.COMPLETED);
      updateReviewData();
    }
  };

  const handleAnswerSelect = (selectedIndex) => {
    if (selectedAnswer !== null) return;
    
    setSelectedAnswer(selectedIndex);
    const result = gameController.submitAnswer(selectedIndex);
    setFeedback(result);
    
    // 记录复习完成
    const reviewItem = gameController.currentReviewItem;
    if (reviewItem) {
      reviewQueueManager.recordReviewCompletion(
        reviewItem,
        result.isCorrect,
        result.responseTime
      );
    }
    
    // 延迟加载下一题
    setTimeout(() => {
      loadNextQuestion();
    }, 3000);
  };

  const getPriorityColor = (priority) => {
    const colors = {
      [REVIEW_PRIORITY.URGENT]: { bg: '#fed7d7', border: '#e53e3e', text: '#c53030' },
      [REVIEW_PRIORITY.HIGH]: { bg: '#feebc8', border: '#dd6b20', text: '#c05621' },
      [REVIEW_PRIORITY.MEDIUM]: { bg: '#fefcbf', border: '#d69e2e', text: '#b7791f' },
      [REVIEW_PRIORITY.LOW]: { bg: '#c6f6d5', border: '#38a169', text: '#2f855a' },
      [REVIEW_PRIORITY.OPTIONAL]: { bg: '#bee3f8', border: '#3182ce', text: '#2c5282' }
    };
    return colors[priority] || colors[REVIEW_PRIORITY.OPTIONAL];
  };

  const getPriorityLabel = (priority) => {
    const labels = {
      [REVIEW_PRIORITY.URGENT]: '紧急',
      [REVIEW_PRIORITY.HIGH]: '高优先级',
      [REVIEW_PRIORITY.MEDIUM]: '中等',
      [REVIEW_PRIORITY.LOW]: '低优先级',
      [REVIEW_PRIORITY.OPTIONAL]: '可选'
    };
    return labels[priority] || priority;
  };

  if (!reviewStats || !dailyProgress) {
    return <div>加载中...</div>;
  }

  return (
    <PageContainer>
      <Header>
        <HeaderTitle>
          <Target size={28} />
          智能复习系统
        </HeaderTitle>
        
        <StatsGrid>
          <StatCard color="#667eea">
            <StatLabel>今日进度</StatLabel>
            <StatValue>
              {dailyProgress.completed} / {dailyProgress.goal}
              <CheckCircle size={20} />
            </StatValue>
          </StatCard>
          
          <StatCard color="#38a169">
            <StatLabel>今日准确率</StatLabel>
            <StatValue>
              {(dailyProgress.accuracy * 100).toFixed(1)}%
              <TrendingUp size={20} />
            </StatValue>
          </StatCard>
          
          <StatCard color="#d69e2e">
            <StatLabel>待复习总数</StatLabel>
            <StatValue>
              {reviewStats.totalPending}
              <BarChart3 size={20} />
            </StatValue>
          </StatCard>
          
          <StatCard color="#e53e3e">
            <StatLabel>预计用时</StatLabel>
            <StatValue>
              {Math.ceil(reviewStats.estimatedTotalTime / 60)}分钟
              <Clock size={20} />
            </StatValue>
          </StatCard>
        </StatsGrid>

        <ProgressSection>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <span style={{ fontWeight: 600, color: '#2d3748' }}>每日目标进度</span>
            <span style={{ color: '#718096' }}>
              {dailyProgress.completed} / {dailyProgress.goal}
            </span>
          </div>
          <ProgressBar>
            <ProgressFill progress={(dailyProgress.completed / dailyProgress.goal) * 100} />
          </ProgressBar>
        </ProgressSection>
      </Header>

      <PrioritySection>
        <PriorityTitle>
          <AlertCircle size={20} />
          按优先级复习
        </PriorityTitle>
        
        <PriorityGrid>
          {Object.values(REVIEW_PRIORITY).map(priority => {
            const count = reviewStats.byPriority[priority] || 0;
            const colors = getPriorityColor(priority);
            
            return (
              <PriorityCard
                key={priority}
                bgColor={colors.bg}
                borderColor={colors.border}
                count={count}
                onClick={() => count > 0 && startReview(priority)}
              >
                <PriorityLabel>{getPriorityLabel(priority)}</PriorityLabel>
                <PriorityCount color={colors.text}>{count}</PriorityCount>
              </PriorityCard>
            );
          })}
        </PriorityGrid>
      </PrioritySection>

      <GameSection>
        {gameState === GAME_STATE.IDLE && (
          <div style={{ textAlign: 'center' }}>
            <h2 style={{ color: '#2d3748', marginBottom: '1rem' }}>开始智能复习</h2>
            <p style={{ color: '#718096', marginBottom: '2rem' }}>
              系统将根据遗忘曲线和学习状态为您推荐最需要复习的句子
            </p>
            <StartButton 
              onClick={() => startReview()}
              disabled={reviewStats.totalPending === 0}
            >
              <Target size={20} />
              开始复习 ({reviewStats.totalPending} 个待复习)
            </StartButton>
          </div>
        )}

        {gameState === GAME_STATE.QUESTION && currentQuestion && currentOptions && (
          <>
            <QuestionArea
              sentence={currentQuestion.english}
              pronunciation={currentQuestion.pronunciation}
              chinesePronunciation={currentQuestion.chinesePronunciation}
              onDetailClick={onSentenceDetail}
              sentenceId={currentQuestion.id}
              autoPlay={true}
            />
            <OptionsArea
              options={currentOptions.options}
              selectedIndex={selectedAnswer}
              correctIndex={feedback?.correctIndex}
              onSelect={handleAnswerSelect}
              showResult={feedback !== null}
            />
            {feedback && (
              <FeedbackArea
                isCorrect={feedback.isCorrect}
                explanation={feedback.explanation}
                correctAnswer={feedback.correctAnswer}
                responseTime={feedback.responseTime}
              />
            )}
          </>
        )}

        {gameState === GAME_STATE.COMPLETED && (
          <div style={{ textAlign: 'center' }}>
            <h2 style={{ color: '#2d3748', marginBottom: '1rem' }}>复习完成！</h2>
            <p style={{ color: '#718096', marginBottom: '2rem' }}>
              恭喜您完成了这轮复习，继续保持学习的好习惯！
            </p>
            <StartButton onClick={() => setGameState(GAME_STATE.IDLE)}>
              返回复习首页
            </StartButton>
          </div>
        )}
      </GameSection>
    </PageContainer>
  );
};

export default ReviewPage;

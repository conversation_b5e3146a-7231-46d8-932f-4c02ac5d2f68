/**
 * Firebase配置和初始化
 * 提供Firestore数据库连接和基础配置
 */

import { initializeApp } from 'firebase/app';
import { getFirestore, connectFirestoreEmulator } from 'firebase/firestore';

// Firebase配置
// 注意：这些是公开的配置，不包含敏感信息
const firebaseConfig = {
  // 开发环境配置 - 请替换为你的Firebase项目配置
  apiKey: import.meta.env.VITE_FIREBASE_API_KEY || "demo-api-key",
  authDomain: import.meta.env.VITE_FIREBASE_AUTH_DOMAIN || "sentence-alchemist-demo.firebaseapp.com",
  projectId: import.meta.env.VITE_FIREBASE_PROJECT_ID || "sentence-alchemist-demo",
  storageBucket: import.meta.env.VITE_FIREBASE_STORAGE_BUCKET || "sentence-alchemist-demo.appspot.com",
  messagingSenderId: import.meta.env.VITE_FIREBASE_MESSAGING_SENDER_ID || "123456789",
  appId: import.meta.env.VITE_FIREBASE_APP_ID || "1:123456789:web:abcdef123456"
};

// 初始化Firebase应用
const app = initializeApp(firebaseConfig);

// 初始化Firestore
const db = getFirestore(app);

// 开发环境下连接到Firestore模拟器（可选）
if (import.meta.env.DEV && import.meta.env.VITE_USE_FIRESTORE_EMULATOR === 'true') {
  try {
    connectFirestoreEmulator(db, 'localhost', 8080);
    console.log('已连接到Firestore模拟器');
  } catch (error) {
    console.warn('连接Firestore模拟器失败:', error);
  }
}

// 导出应用实例和数据库实例
export { app, db };

// 在开发环境下将db实例挂载到window对象，方便控制台调试
if (import.meta.env.DEV) {
  window.db = db;
  // 同时导出Firebase函数到全局，方便控制台使用
  import('firebase/firestore').then(firestore => {
    window.addDoc = firestore.addDoc;
    window.collection = firestore.collection;
    window.doc = firestore.doc;
    window.getDoc = firestore.getDoc;
    window.getDocs = firestore.getDocs;
    window.setDoc = firestore.setDoc;
    window.updateDoc = firestore.updateDoc;
    window.deleteDoc = firestore.deleteDoc;
    console.log('🔧 Firebase调试工具已加载到window对象');
  });
}

/**
 * 检查Firebase连接状态
 * @returns {Promise<boolean>} 连接是否成功
 */
export async function checkFirebaseConnection() {
  try {
    // 检查是否使用demo配置
    if (firebaseConfig.apiKey === "demo-api-key" ||
        firebaseConfig.projectId === "sentence-alchemist-demo") {
      console.warn('Firebase使用demo配置，跳过连接测试');
      return false;
    }

    // 尝试读取一个不存在的文档来测试连接
    const { doc, getDoc } = await import('firebase/firestore');
    const testDoc = doc(db, 'test', 'connection');

    // 设置超时，避免长时间等待
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('连接超时')), 5000);
    });

    await Promise.race([getDoc(testDoc), timeoutPromise]);
    return true;
  } catch (error) {
    console.warn('Firebase连接测试失败，将使用本地数据:', error.message);
    return false;
  }
}

/**
 * 获取Firestore实例
 * @returns {Firestore} Firestore数据库实例
 */
export function getFirestoreInstance() {
  return db;
}
